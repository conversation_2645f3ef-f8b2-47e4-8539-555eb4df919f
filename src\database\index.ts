import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import env from "@/env";
import * as tables from "./tables";
import {
  getConnectionConfig,
  validateDatabaseConfig,
} from "@/lib/database/connection";

const databaseUrl = env.DATABASE_URL;

const connectionConfig = getConnectionConfig();

if (process.env.NODE_ENV === "development") {
  validateDatabaseConfig();
}

const sql = postgres(databaseUrl, connectionConfig);

export const db = drizzle(sql, { schema: { ...tables } });

export { sql };

export const closeDatabase = async () => {
  await sql.end({ timeout: 5 });
};
