import env from "@/env";

/**
 * Detects if the application is running in a serverless environment
 */
function isServerlessEnvironment(): boolean {
  return Boolean(
    process.env.VERCEL ||
      process.env.AWS_LAMBDA_FUNCTION_NAME ||
      process.env.NETLIFY ||
      process.env.RAILWAY_ENVIRONMENT ||
      process.env.FUNCTIONS_EMULATOR ||
      process.env.AZURE_FUNCTIONS_ENVIRONMENT,
  );
}

/**
 * Gets the appropriate database connection configuration based on the environment
 */
export function getConnectionConfig() {
  const isServerless = isServerlessEnvironment();

  if (isServerless) {
    return {
      max: 1,

      idle_timeout: 20,

      max_lifetime: 60 * 30,

      connect_timeout: 30,

      prepare: true,

      onnotice: () => {},
    };
  }

  return {
    max: env.DB_POOL_SIZE,

    idle_timeout: env.DB_IDLE_TIMEOUT,

    max_lifetime: env.DB_MAX_LIFETIME,

    connect_timeout: env.DB_CONNECT_TIMEOUT,

    prepare: true,


    debug: process.env.NODE_ENV === "development",

    onnotice: process.env.NODE_ENV === "development" ? console.log : () => {},
  };
}

/**
 * Gets the current environment type for logging and monitoring
 */
export function getEnvironmentType(): "serverless" | "traditional" {
  return isServerlessEnvironment() ? "serverless" : "traditional";
}

let configValidated = false;

/**
 * Validates the database configuration
 */
export function validateDatabaseConfig(): void {
  if (configValidated) {
    return;
  }

  const config = getConnectionConfig();
  const envType = getEnvironmentType();

  console.log(`Database configuration loaded for ${envType} environment.`);

  if (envType === "serverless" && config.max && config.max > 2) {
    console.warn(
      "Warning: High connection pool size detected in serverless environment",
    );
  }

  if (envType === "traditional" && config.max && config.max < 5) {
    console.warn(
      "Warning: Low connection pool size for traditional server environment",
    );
  }

  configValidated = true;
}

/**
 * Resets the configuration validation flag (for testing purposes)
 */
export function resetConfigValidation(): void {
  configValidated = false;
}
