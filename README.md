# zen-starter

A modern Next.js starter with <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (Postgres), Tai<PERSON>wind and batteries-included tooling.

## Tech stack

- Next.js 15 (<PERSON>pp Router, RSC)
- React 19.2
- PostgreSQL + Drizzle ORM 
- Better-Auth 
- Tailwind CSS v4
- shadcn/ui
- Creem integration (One-time payments, Subscriptions, Customer portal, Webhooks)

## TODO

- [done] Adding auth ratelimit 
- [done:not-fully-tested] Adding dynamic site settings support (KV, Vercel Edge Config, File, DB)
- [on-going] Adding support for multiple file storages: +S3, +R2, +GCS
- [on-going] Adding support for multiple DB engines: +MySQL 
- [on-going] Adding site settings layout and UI
- Add i18n support using cookies for storage and query parameters (e.g., ?hl=en) instead of path-based style (e.g., /en/)
- Updating auth pages UI (there is a preview at /test)
- Adding organization to auth
- Adding onboarding pages (asking user for account type .. individual, team, organization and other stuff)
- Updating dashboard layouts and adjust sections (extra/less) based on account type (Individual, Team, Organization)
- Adding SSO (SAML and OPENiD Connect) for organization accounts
- Adding multiple payment providers (Paypal, Stripe, etc.)

## Quick start (development)

1. Clone the repo and change directory:

```powershell
git clone https://github.com/devknown/zen-starter
cd zen-starter
```

2. Install dependencies with pnpm:

```powershell
pnpm install
```

3. Create an `.env` file in the project root and set required environment variables (for example `DATABASE_URL`, secrets for email providers, S3 credentials, etc.). The project expects environment values used by Next.js and scripts in `scripts/`.

4. Drizzle commands:

```powershell
pnpm run db:generate
pnpm run db:migrate:dev
```
use `pnpm run db:migrate:prod` for production

5. Run the development server:
```powershell
pnpm run dev
```

Open http://localhost:3000 in your browser.

### Create an Admin Account

1. **Sign up as a normal user**  
   - Open the website UI and create an account using your email, e.g. `<EMAIL>`.

2. **Activate your account**  
   - In development mode, the magic link for login will appear in the terminal.  
   - Copy and open that link in your browser to activate your account.

3. **Promote the account to admin**  
   - Run the appropriate command with your email:

     - **Development**  
       ```bash
       pnpm run set:admin --email=<EMAIL>
       ```

     - **Production**  
       ```bash
       pnpm run set:admin:prod --email=<EMAIL>
       ```

### Manage the blog

In local development, open http://localhost:3000/keystatic to access the Keystatic dashboard.

### Scripts

Run scripts with `pnpm run <script>`.

- `dev` — start Next.js in development mode (with Turbo): `pnpm run dev`
- `build` — build the Next.js app for production: `pnpm run build`
- `start` — start the production server (after `build`): `pnpm run start`
- `lint` — run ESLint: `pnpm run lint`
- `prettier:check` — check formatting with Prettier: `pnpm run prettier:check`
- `prettier:format` — format the repository with Prettier: `pnpm run prettier:format`
- `analyze` — build with bundle analysis enabled: `pnpm run analyze`
- `analyze:dev` — start dev server with bundle analysis environment variable set: `pnpm run analyze:dev`

## Database (Drizzle)

This project uses Drizzle for migrations and schema generation. The scripts in `package.json` include:

- `db:generate` — generate types / migration artifacts using `src/database/config.ts`.
- `db:generate:prod` — same but using `src/database/config.prod.ts`.
- `db:migrate:dev` — run migrations using development config.
- `db:migrate:prod` — run migrations using production config.
- `db:push` — push the schema to the database using development config.

Examples:

```powershell
pnpm run db:generate
pnpm run db:migrate:dev
pnpm run db:push
```

## Formatting & linting

Use the included scripts to keep code consistent:

```powershell
pnpm run prettier:check
pnpm run prettier:format
pnpm run lint
```

The project also includes `prettier-plugin-tailwindcss` and `eslint-config-prettier` in `devDependencies`.

## Production

1. Build

```powershell
pnpm run build
```

2. Start (after build)

```powershell
pnpm run start
```

Deploy to your hosting provider of choice (Vercel, Fly, Render, etc.). Environment variables and database connection strings should be configured in the hosting environment.

## Where to look next

- Application source: `src/` (Next.js app routes and components).
- Database config and schema: `src/database/`.
- Keystatic config: `keystatic.config.ts` and `src/app/keystatic/`.

## References

- Next.js: https://nextjs.org/
- pnpm: https://pnpm.io/
- Drizzle (drizzle-kit): https://orm.drizzle.team/
- Keystatic: https://keystatic.com/

---
